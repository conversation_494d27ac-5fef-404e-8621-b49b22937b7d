{"userId": *********, "ssecurity": "AC/Lq6Ic2j/cUx0FoXwfDQ==", "deviceId": "szv1X8Sg9xKpa5cH", "serviceToken": "o/uaRJ/YCRSYh/pxt8IMFs2cNTC7Y8sw1xPt7qtfmZurS4CJu709W2qZBAA/oGWu12f6Y29YVcgZS6r8fPPkqZamQiI7kc/ZwLLvbGkLDXL+HalJnh7QinOzdG6csc+NDGBfgFDtNwSWxIe/EMi5i/3r5wWuIUgRMSeqUpcUcTS9wD+S5lG0CiVxwSjhcEL9", "cUserId": "J2df71NwQIIJ7HQ20Yc8nOT5Yzg", "expireTime": "2025-08-28 21:50:26", "account_info": {"userId": *********, "nickName": "o_。怪怪", "gender": "m", "icon": "https://cdn.cnbj1.fds.api.mi-img.com/user-avatar/ac0ca01b-88d7-4280-9775-36465e17f625.jpg", "account": "+86 176****3217", "safePhone": "+86 176****3217", "safePhoneAddressKey": "E0E694903D5D2A66", "hasBindSafePhone": true, "phoneModifyTime": *************, "safeEmail": "jen***g@f*****l.com", "emailModifyTime": *************, "hasBindSafeEmail": true, "hasSetPwd": false, "pwdUpdateTime": 0, "hasSetMibao": false, "profileBlocked": false, "snsBindInfo": {}, "openAppInfo": [], "region": "CN", "twoFactorAuth": 0, "securityLevel": 0, "countryListOnlyCN": false, "showBindSafePhone": false, "showMibao": false}}